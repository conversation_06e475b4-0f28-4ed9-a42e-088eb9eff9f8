import { NextRequest, NextResponse } from 'next/server';
import { getUserByUsername } from '@/lib/database';
import bcrypt from 'bcryptjs';

/**
 * POST /api/auth/signin - Authenticate user
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password, recoveryKey } = body;
    
    // Validate required fields
    if (!username || (!password && !recoveryKey)) {
      return NextResponse.json(
        { error: 'Username and either password or recovery key are required' },
        { status: 400 }
      );
    }
    
    // Get user from database
    const user = await getUserByUsername(username);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid username or password' },
        { status: 401 }
      );
    }
    
    // Verify password or recovery key
    let authenticationValid = false;

    if (password) {
      authenticationValid = await bcrypt.compare(password, user.password);
    } else if (recoveryKey && user.recovery_key) {
      authenticationValid = recoveryKey === user.recovery_key;
    }

    if (!authenticationValid) {
      return NextResponse.json(
        { error: 'Invalid username, password, or recovery key' },
        { status: 401 }
      );
    }

    // Authentication successful
    return NextResponse.json(
      { 
        message: 'Sign in successful',
        user: {
          id: user.id,
          username: user.username,
          role: user.role,
          created_at: user.created_at
        }
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error during sign in:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
