"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Search, SortAsc, SortDesc, Filter, X } from "lucide-react";

export interface SortOption {
  value: string;
  label: string;
  field: string;
}

export interface FilterOption {
  value: string;
  label: string;
  field: string;
}

export interface SearchSortControlsProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  sortOptions: SortOption[];
  sortBy: string;
  sortOrder: "asc" | "desc";
  onSortChange: (field: string, order: "asc" | "desc") => void;
  filterOptions?: FilterOption[];
  activeFilters?: string[];
  onFilterChange?: (filters: string[]) => void;
  placeholder?: string;
  className?: string;
}

export function SearchSortControls({
  searchValue,
  onSearchChange,
  sortOptions,
  sortBy,
  sortOrder,
  onSortChange,
  filterOptions = [],
  activeFilters = [],
  onFilterChange,
  placeholder = "Search...",
  className = "",
}: SearchSortControlsProps) {
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const currentSortOption = sortOptions.find(
    (option) => option.field === sortBy
  );

  const handleFilterToggle = (filterValue: string) => {
    if (!onFilterChange) return;

    const newFilters = activeFilters.includes(filterValue)
      ? activeFilters.filter((f) => f !== filterValue)
      : [...activeFilters, filterValue];

    onFilterChange(newFilters);
  };

  const clearAllFilters = () => {
    if (onFilterChange) {
      onFilterChange([]);
    }
  };

  const clearSearch = () => {
    onSearchChange("");
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and Sort Row */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search Input */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={placeholder}
            value={searchValue}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 pr-10"
          />
          {searchValue && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Sort Controls */}
        <div className="flex gap-2">
          <Select
            value={sortBy}
            onValueChange={(value) => onSortChange(value, sortOrder)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by..." />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.field}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              onSortChange(sortBy, sortOrder === "asc" ? "desc" : "asc")
            }
            className="px-3"
          >
            {sortOrder === "asc" ? (
              <SortAsc className="h-4 w-4" />
            ) : (
              <SortDesc className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Filter Controls */}
        {filterOptions.length > 0 && (
          <DropdownMenu open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Filter
                {activeFilters.length > 0 && (
                  <Badge
                    variant="secondary"
                    className="ml-1 h-5 min-w-[20px] text-xs"
                  >
                    {activeFilters.length}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {filterOptions.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  onClick={() => handleFilterToggle(option.value)}
                  className="flex items-center justify-between"
                >
                  <span>{option.label}</span>
                  {activeFilters.includes(option.value) && (
                    <div className="h-2 w-2 bg-primary rounded-full" />
                  )}
                </DropdownMenuItem>
              ))}
              {activeFilters.length > 0 && (
                <>
                  <div className="border-t my-1" />
                  <DropdownMenuItem
                    onClick={clearAllFilters}
                    className="text-destructive"
                  >
                    Clear all filters
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* Active Filters Display */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {activeFilters.map((filterValue) => {
            const filterOption = filterOptions.find(
              (opt) => opt.value === filterValue
            );
            return (
              <Badge
                key={filterValue}
                variant="secondary"
                className="flex items-center gap-1"
              >
                {filterOption?.label || filterValue}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleFilterToggle(filterValue)}
                  className="h-3 w-3 p-0 hover:bg-muted ml-1"
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            );
          })}
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
          >
            Clear all
          </Button>
        </div>
      )}

      {/* Search/Filter Summary */}
      {(searchValue || activeFilters.length > 0) && (
        <div className="text-sm text-muted-foreground">
          {searchValue && (
            <span>
              Searching for "{searchValue}"
              {activeFilters.length > 0 && " with filters applied"}
            </span>
          )}
          {!searchValue && activeFilters.length > 0 && (
            <span>Showing filtered results</span>
          )}
        </div>
      )}
    </div>
  );
}
