const sqlite3 = require("sqlite3").verbose();
const path = require("path");

// Database path - use the same path as the application
const dbPath = path.join(__dirname, "..", "data", "ldis.db");

console.log("🔄 Starting migration to add missing columns...");
console.log("📍 Database path:", dbPath);

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error("❌ Error opening database:", err.message);
    process.exit(1);
  }
  console.log("✅ Connected to SQLite database");
});

// Run the migration
async function runMigration() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log("📝 Adding approved_by column to documents table...");

      // Add approved_by column to documents table if it doesn't exist
      db.run(
        `
        ALTER TABLE documents 
        ADD COLUMN approved_by INTEGER 
        REFERENCES users(id) ON DELETE SET NULL
      `,
        (err) => {
          if (err && !err.message.includes("duplicate column name")) {
            console.error(
              "❌ Error adding approved_by column to documents:",
              err.message
            );
            reject(err);
            return;
          }

          if (err && err.message.includes("duplicate column name")) {
            console.log(
              "ℹ️  approved_by column already exists in documents table"
            );
          } else {
            console.log("✅ approved_by column added to documents table");
          }

          console.log("📝 Adding status column to archives table...");

          // Add status column to archives table if it doesn't exist
          db.run(
            `
          ALTER TABLE archives 
          ADD COLUMN status TEXT DEFAULT 'approved'
        `,
            (err) => {
              if (err && !err.message.includes("duplicate column name")) {
                console.error(
                  "❌ Error adding status column to archives:",
                  err.message
                );
                reject(err);
                return;
              }

              if (err && err.message.includes("duplicate column name")) {
                console.log(
                  "ℹ️  status column already exists in archives table"
                );
              } else {
                console.log("✅ status column added to archives table");
              }

              console.log(
                "📝 Updating existing archive records with default status..."
              );

              // Update existing archive records to have 'approved' status
              db.run(
                `
            UPDATE archives 
            SET status = 'approved' 
            WHERE status IS NULL OR status = ''
          `,
                (err) => {
                  if (err) {
                    console.error(
                      "❌ Error updating archive statuses:",
                      err.message
                    );
                    reject(err);
                    return;
                  }

                  console.log(
                    "✅ Updated existing archive records with approved status"
                  );

                  // Verify the changes
                  console.log("🔍 Verifying migration results...");

                  db.get(`PRAGMA table_info(documents)`, (err, rows) => {
                    if (err) {
                      console.error(
                        "❌ Error checking documents table:",
                        err.message
                      );
                      reject(err);
                      return;
                    }

                    db.all(`PRAGMA table_info(documents)`, (err, docRows) => {
                      if (err) {
                        console.error(
                          "❌ Error checking documents table:",
                          err.message
                        );
                        reject(err);
                        return;
                      }

                      const hasApprovedBy = docRows.some(
                        (row) => row.name === "approved_by"
                      );
                      console.log(
                        `📊 Documents table approved_by column: ${
                          hasApprovedBy ? "✅ Present" : "❌ Missing"
                        }`
                      );

                      db.all(`PRAGMA table_info(archives)`, (err, archRows) => {
                        if (err) {
                          console.error(
                            "❌ Error checking archives table:",
                            err.message
                          );
                          reject(err);
                          return;
                        }

                        const hasStatus = archRows.some(
                          (row) => row.name === "status"
                        );
                        console.log(
                          `📊 Archives table status column: ${
                            hasStatus ? "✅ Present" : "❌ Missing"
                          }`
                        );

                        if (hasApprovedBy && hasStatus) {
                          console.log("🎉 Migration completed successfully!");
                          resolve();
                        } else {
                          reject(new Error("Migration verification failed"));
                        }
                      });
                    });
                  });
                }
              );
            }
          );
        }
      );
    });
  });
}

// Execute migration
runMigration()
  .then(() => {
    console.log("✅ Migration completed successfully");
    db.close((err) => {
      if (err) {
        console.error("❌ Error closing database:", err.message);
        process.exit(1);
      }
      console.log("🔒 Database connection closed");
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error("❌ Migration failed:", error.message);
    db.close((err) => {
      if (err) {
        console.error("❌ Error closing database:", err.message);
      }
      process.exit(1);
    });
  });
