"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  XCircle,
  FileText,
  Calendar,
  User,
  Hash,
} from "lucide-react";
import { ThemeProvider } from "@/components/theme-provider";
import Image from "next/image";

interface ValidationData {
  id: number;
  document_name: string;
  applicant_name: string;
  status: string;
  approved_at?: string;
  code: string;
}

interface ValidationResponse {
  valid: boolean;
  type?: "document" | "archive";
  data?: ValidationData;
  error?: string;
}

export default function ValidatePage() {
  const params = useParams();
  const code = params.code as string;
  const [validation, setValidation] = useState<ValidationResponse | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const validateDocument = async () => {
      try {
        const response = await fetch(`/api/validate/${code}`);
        const data = await response.json();
        setValidation(data);
      } catch (error) {
        console.error("Validation error:", error);
        setValidation({
          valid: false,
          error: "Failed to validate document",
        });
      } finally {
        setLoading(false);
      }
    };

    if (code) {
      validateDocument();
    }
  }, [code]);

  if (loading) {
    return (
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Validating document...</p>
          </div>
        </div>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <div className="min-h-screen bg-background py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl mx-auto">
          {/* Header with LDIS branding */}
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center gap-3">
              <Image
                src="/images/LDIS.png"
                alt="LDIS Logo"
                width={40}
                height={40}
                className="h-10 w-10"
              />
              <div>
                <h1 className="text-3xl font-bold text-foreground mb-1">
                  Document Validation
                </h1>
                <p className="text-muted-foreground">
                  Legal Document Issuance System (LDIS)
                </p>
              </div>
            </div>
          </div>

          <Card className="shadow-lg border-border">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                {validation?.valid ? (
                  <CheckCircle className="h-16 w-16 text-green-500" />
                ) : (
                  <XCircle className="h-16 w-16 text-red-500" />
                )}
              </div>
              <CardTitle className="text-2xl text-foreground">
                {validation?.valid ? "Valid Document" : "Invalid Document"}
              </CardTitle>
            </CardHeader>

            <CardContent className="space-y-6">
              {validation?.valid && validation.data ? (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3">
                      <Hash className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Code
                        </p>
                        <p className="text-lg font-mono text-foreground">
                          {validation.data.code}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Document Type
                        </p>
                        <Badge variant="outline" className="mt-1">
                          {validation.type === "archive"
                            ? "Archived"
                            : "Active"}
                        </Badge>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Document Name
                        </p>
                        <p className="text-lg text-foreground">
                          {validation.data.document_name}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <User className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Applicant
                        </p>
                        <p className="text-lg text-foreground">
                          {validation.data.applicant_name}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <CheckCircle className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Status
                        </p>
                        <Badge
                          variant={
                            validation.data.status === "approved"
                              ? "default"
                              : "secondary"
                          }
                          className="mt-1"
                        >
                          {validation.data.status.charAt(0).toUpperCase() +
                            validation.data.status.slice(1)}
                        </Badge>
                      </div>
                    </div>

                    {validation.data.approved_at && (
                      <div className="flex items-center space-x-3">
                        <Calendar className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">
                            Approved Date
                          </p>
                          <p className="text-lg text-foreground">
                            {new Date(
                              validation.data.approved_at
                            ).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <p className="text-green-800 dark:text-green-200 font-medium">
                        This document is valid and has been officially approved.
                      </p>
                    </div>
                  </div>
                </>
              ) : (
                <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <div className="flex items-center">
                    <XCircle className="h-5 w-5 text-red-500 mr-2" />
                    <div>
                      <p className="text-red-800 dark:text-red-200 font-medium">
                        Document validation failed
                      </p>
                      <p className="text-red-600 dark:text-red-300 text-sm mt-1">
                        {validation?.error ||
                          "The document code is invalid or the document has not been approved."}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="border-t border-border pt-4">
                <p className="text-xs text-muted-foreground text-center">
                  Code: {code} • Validated on {new Date().toLocaleString()}
                </p>
              </div>
            </CardContent>
          </Card>

          <div className="text-center mt-8">
            <p className="text-sm text-muted-foreground">
              Legal Document Issuance System (LDIS)
            </p>
            <p className="text-xs text-muted-foreground/70 mt-1">
              For questions about this document, please contact the issuing
              authority.
            </p>
          </div>
        </div>
      </div>
    </ThemeProvider>
  );
}
