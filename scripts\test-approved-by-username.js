#!/usr/bin/env node

/**
 * Test script to verify approved_by username functionality
 */

require("ts-node/register");

async function testApprovedByUsername() {
  try {
    console.log("🧪 Testing Approved By Username Functionality...\n");

    const {
      initializeDatabase,
      createDocument,
      getDocumentById,
      createArchive,
      getAllArchives,
      getArchivesWithUserInfo,
      deleteDocument,
      getAllUsers,
      closeDatabase,
    } = require("../src/lib/database.ts");

    // Initialize database
    console.log("📦 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized\n");

    // Get users
    const users = await getAllUsers();
    if (users.length === 0) {
      console.log("❌ No users found. Please create a user first.");
      return;
    }
    const testUser = users[0];
    console.log(`✅ Using test user: ${testUser.username} (ID: ${testUser.id})\n`);

    // Create a test document
    console.log("📄 Creating test document...");
    const documentId = await createDocument(
      "Approved By Test Document",
      "Test Applicant",
      Buffer.from("Test document data for approved by test"),
      "approved",
      testUser.id
    );
    console.log(`✅ Test document created with ID: ${documentId}\n`);

    // Get the document
    const document = await getDocumentById(documentId);
    console.log(`✅ Document verified: ${document?.document_name}\n`);

    // Create archive entry with approved_by set to current user
    console.log("📦 Creating archive entry with approved_by username...");
    const archiveId = await createArchive(
      document.document_name,
      document.applicant_name,
      document.document_data,
      document.user_id,
      document.id,
      document.approved_at || new Date().toISOString(),
      testUser.id // Set the current user as the approver
    );
    console.log(`✅ Archive created with ID: ${archiveId}\n`);

    // Test getting archives with user info
    console.log("👥 Getting archives with user info...");
    const archivesWithInfo = await getArchivesWithUserInfo();
    console.log(`✅ Found ${archivesWithInfo.length} archives with user info\n`);

    // Find our test archive
    const testArchive = archivesWithInfo.find(a => a.id === archiveId);
    if (testArchive) {
      console.log("📋 Archive Details:");
      console.log(`   Document Name: ${testArchive.document_name}`);
      console.log(`   Applicant Name: ${testArchive.applicant_name}`);
      console.log(`   Uploaded By: ${testArchive.username || 'Unknown'}`);
      console.log(`   Approved By: ${testArchive.approved_by_username || 'Unknown'}`);
      console.log(`   Approved At: ${testArchive.approved_at || 'Not set'}`);
      console.log(`   Approved By ID: ${testArchive.approved_by || 'Not set'}\n`);

      // Verify the approved_by username is correct
      if (testArchive.approved_by_username === testUser.username) {
        console.log("🎉 SUCCESS: Approved by username is correctly stored and retrieved!");
      } else {
        console.log(`❌ FAILURE: Expected approved_by_username to be '${testUser.username}', got '${testArchive.approved_by_username}'`);
      }
    } else {
      console.log("❌ FAILURE: Could not find the test archive");
    }

    // Delete the original document
    console.log("\n🗑️ Deleting original document...");
    await deleteDocument(documentId);
    console.log("✅ Original document deleted\n");

    // Verify archive still exists with user info
    console.log("🔍 Verifying archive persistence with user info...");
    const archivesAfterDelete = await getArchivesWithUserInfo();
    const persistentArchive = archivesAfterDelete.find(a => a.id === archiveId);
    
    if (persistentArchive && persistentArchive.approved_by_username === testUser.username) {
      console.log("🎉 SUCCESS: Archive persisted with correct approved_by username after document deletion!");
    } else {
      console.log("❌ FAILURE: Archive or approved_by username not preserved after document deletion");
    }

    console.log("\n📊 Summary:");
    console.log(`   Archive ID: ${archiveId}`);
    console.log(`   Approved By Username: ${persistentArchive?.approved_by_username || 'Not found'}`);
    console.log(`   Expected Username: ${testUser.username}`);
    console.log(`   Test Result: ${persistentArchive?.approved_by_username === testUser.username ? '✅ PASSED' : '❌ FAILED'}`);

    await closeDatabase();
  } catch (error) {
    console.error("❌ Error in approved by username test:", error);
    process.exit(1);
  }
}

testApprovedByUsername();
