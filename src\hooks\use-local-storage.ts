"use client";

import { useState, useEffect } from "react";

/**
 * Custom hook for localStorage with SSR support
 * @param key - The localStorage key
 * @param initialValue - The initial value to use if no value exists in localStorage
 * @returns [value, setValue] - The current value and a setter function
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void] {
  // State to store our value
  // Always start with initialValue to prevent hydration mismatch
  const [storedValue, setStoredValue] = useState<T>(initialValue);

  // Effect to load from localStorage after hydration and listen for changes
  useEffect(() => {
    if (typeof window === "undefined") return;

    // Load initial value from localStorage
    try {
      const item = window.localStorage.getItem(key);
      if (item) {
        const parsedValue = JSON.parse(item);
        setStoredValue(parsedValue);
      }
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
    }

    // Listen for localStorage changes from other tabs/components
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          const parsedValue = JSON.parse(e.newValue);
          setStoredValue(parsedValue);
        } catch (error) {
          console.warn(`Error parsing localStorage change for key "${key}":`, error);
        }
      }
    };

    // Listen for custom storage events (for same-tab changes)
    const handleCustomStorageChange = (e: CustomEvent) => {
      if (e.detail.key === key) {
        setStoredValue(e.detail.value);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('localStorageChange', handleCustomStorageChange as EventListener);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('localStorageChange', handleCustomStorageChange as EventListener);
    };
  }, [key]);

  // Return a wrapped version of useState's setter function that ...
  // ... persists the new value to localStorage.
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      // Save state
      setStoredValue(valueToStore);
      // Save to local storage
      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));

        // Dispatch custom event for same-tab synchronization
        const customEvent = new CustomEvent('localStorageChange', {
          detail: { key, value: valueToStore }
        });
        window.dispatchEvent(customEvent);
      }
    } catch (error) {
      // A more advanced implementation would handle the error case
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}

/**
 * Hook specifically for admin authentication state
 * Manages both admin mode and authentication status in localStorage
 */
export function useAdminAuth() {
  const [adminMode, setAdminMode] = useLocalStorage("ldis-admin-mode", false);
  const [isAuthenticated, setIsAuthenticated] = useLocalStorage("ldis-admin-authenticated", false);
  const [authTimestamp, setAuthTimestamp] = useLocalStorage("ldis-auth-timestamp", 0);
  const [isLoading, setIsLoading] = useState(true);

  // Check if authentication has expired (24 hours)
  const AUTH_EXPIRY_HOURS = 24;
  const isAuthExpired = () => {
    if (!authTimestamp) return true;
    const now = Date.now();
    const expiryTime = authTimestamp + (AUTH_EXPIRY_HOURS * 60 * 60 * 1000);
    return now > expiryTime;
  };

  // Effect to check auth expiry on mount and clear if expired
  useEffect(() => {
    const checkExpiry = () => {
      if (!authTimestamp) return true;
      const now = Date.now();
      const expiryTime = authTimestamp + (AUTH_EXPIRY_HOURS * 60 * 60 * 1000);
      return now > expiryTime;
    };

    if (isAuthenticated && checkExpiry()) {
      setIsAuthenticated(false);
      setAdminMode(false);
      setAuthTimestamp(0);
    }

    // Set loading to false after initial check
    setIsLoading(false);
  }, [isAuthenticated, authTimestamp, setIsAuthenticated, setAdminMode, setAuthTimestamp]);

  const authenticate = () => {
    setIsAuthenticated(true);
    setAuthTimestamp(Date.now());
    setAdminMode(true);
  };

  const logout = () => {
    setIsAuthenticated(false);
    setAdminMode(false);
    setAuthTimestamp(0);
  };

  const toggleAdminMode = (enabled: boolean) => {
    if (enabled && !isAuthenticated) {
      // Return false to indicate authentication is required
      return false;
    }
    setAdminMode(enabled);
    return true;
  };

  return {
    adminMode,
    isAuthenticated,
    authenticate,
    logout,
    toggleAdminMode,
    isAuthExpired: isAuthExpired(),
    isLoading,
  };
}

/**
 * Hook for user authentication state (both admin and regular users)
 * Manages user authentication and role information
 */
export function useUserAuth() {
  const [user, setUser] = useLocalStorage<{
    id: number;
    username: string;
    role: 'admin' | 'regular';
    gmail?: string;
  } | null>("ldis-user", null);
  const [isAuthenticated, setIsAuthenticated] = useLocalStorage("ldis-user-authenticated", false);
  const [authTimestamp, setAuthTimestamp] = useLocalStorage("ldis-user-auth-timestamp", 0);
  const [isLoading, setIsLoading] = useState(true);

  // Check if authentication has expired (24 hours)
  const AUTH_EXPIRY_HOURS = 24;
  const isAuthExpired = () => {
    if (!authTimestamp) return true;
    const now = Date.now();
    const expiryTime = authTimestamp + (AUTH_EXPIRY_HOURS * 60 * 60 * 1000);
    return now > expiryTime;
  };

  // Effect to check auth expiry on mount and clear if expired
  useEffect(() => {
    if (isAuthenticated && isAuthExpired()) {
      setIsAuthenticated(false);
      setUser(null);
      setAuthTimestamp(0);
    }
    setIsLoading(false);
  }, [isAuthenticated, authTimestamp, setIsAuthenticated, setUser, setAuthTimestamp]);

  const authenticate = (userData: {
    id: number;
    username: string;
    role: 'admin' | 'regular';
    gmail?: string;
  }) => {
    setUser(userData);
    setIsAuthenticated(true);
    setAuthTimestamp(Date.now());
  };

  const logout = () => {
    setIsAuthenticated(false);
    setUser(null);
    setAuthTimestamp(0);
  };

  return {
    user,
    isAuthenticated,
    authenticate,
    logout,
    isAuthExpired: isAuthExpired(),
    isLoading,
    isAdmin: user?.role === 'admin',
    isRegular: user?.role === 'regular',
  };
}
