const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'ldis.db');

console.log('🔍 Testing Unique Code Generation Across Documents and Archives...');
console.log('📍 Database path:', dbPath);

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Test unique code generation
async function testUniqueCodeGeneration() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log('\n🔍 Checking current code distribution...');
      
      // Check documents with codes
      db.all(`SELECT id, document_name, applicant_name, status, code FROM documents WHERE code IS NOT NULL ORDER BY code`, (err, docs) => {
        if (err) {
          console.error('❌ Error fetching documents:', err.message);
          reject(err);
          return;
        }
        
        // Check archives with codes
        db.all(`SELECT id, document_name, applicant_name, status, code FROM archives WHERE code IS NOT NULL ORDER BY code`, (err, archives) => {
          if (err) {
            console.error('❌ Error fetching archives:', err.message);
            reject(err);
            return;
          }
          
          console.log(`\n📄 Documents with codes: ${docs.length}`);
          docs.forEach((doc, index) => {
            console.log(`${index + 1}. ${doc.document_name} - Code: ${doc.code} (Status: ${doc.status})`);
          });
          
          console.log(`\n📦 Archives with codes: ${archives.length}`);
          archives.forEach((archive, index) => {
            console.log(`${index + 1}. ${archive.document_name} - Code: ${archive.code} (Status: ${archive.status})`);
          });
          
          // Check for duplicate codes between documents and archives
          const docCodes = new Set(docs.map(doc => doc.code));
          const archiveCodes = new Set(archives.map(archive => archive.code));
          const duplicateCodes = [...docCodes].filter(code => archiveCodes.has(code));
          
          console.log(`\n🔍 Code Analysis:`);
          console.log(`• Document codes: ${docCodes.size} unique codes`);
          console.log(`• Archive codes: ${archiveCodes.size} unique codes`);
          console.log(`• Duplicate codes between tables: ${duplicateCodes.length}`);
          
          if (duplicateCodes.length > 0) {
            console.log(`\n⚠️  DUPLICATE CODES FOUND:`);
            duplicateCodes.forEach((code, index) => {
              console.log(`${index + 1}. Code: ${code}`);
              
              // Find documents with this code
              const docsWithCode = docs.filter(doc => doc.code === code);
              const archivesWithCode = archives.filter(archive => archive.code === code);
              
              console.log(`   Documents: ${docsWithCode.map(d => `${d.document_name} (ID: ${d.id})`).join(', ')}`);
              console.log(`   Archives: ${archivesWithCode.map(a => `${a.document_name} (ID: ${a.id})`).join(', ')}`);
            });
            
            console.log(`\n❌ ISSUE: Codes are not unique across documents and archives tables!`);
            console.log(`💡 This can cause problems with QR code validation.`);
          } else {
            console.log(`\n✅ No duplicate codes found between documents and archives.`);
          }
          
          // Test the updated generateDocumentCode function
          console.log(`\n🧪 Testing Updated Code Generation Logic...`);
          console.log(`📝 The generateDocumentCode() function has been updated to:`);
          console.log(`   • Check both documents AND archives tables for existing codes`);
          console.log(`   • Ensure uniqueness across both tables`);
          console.log(`   • Prevent duplicate codes in the future`);
          
          // Show all codes in use
          const allCodes = [...docCodes, ...archiveCodes];
          const uniqueAllCodes = [...new Set(allCodes)];
          
          console.log(`\n📊 Code Statistics:`);
          console.log(`• Total codes in use: ${allCodes.length}`);
          console.log(`• Unique codes across both tables: ${uniqueAllCodes.length}`);
          console.log(`• Code format: DOC-YYYYMMDD-NNNN`);
          
          if (uniqueAllCodes.length > 0) {
            console.log(`\n🔗 All codes in use:`);
            uniqueAllCodes.sort().forEach((code, index) => {
              const inDocs = docCodes.has(code);
              const inArchives = archiveCodes.has(code);
              const location = inDocs && inArchives ? 'BOTH' : inDocs ? 'DOCS' : 'ARCHIVES';
              console.log(`${index + 1}. ${code} (${location})`);
            });
          }
          
          console.log(`\n🎯 Testing Instructions:`);
          console.log(`1. Create a new document in LDIS`);
          console.log(`2. The new code should be unique across both documents and archives`);
          console.log(`3. Archive an existing document`);
          console.log(`4. Restore the archived document`);
          console.log(`5. The restored document should keep the same code (no new code generated)`);
          console.log(`6. All QR codes should work correctly with unique validation URLs`);
          
          console.log(`\n🔧 Technical Implementation:`);
          console.log(`• generateDocumentCode() now checks both tables`);
          console.log(`• Archive process preserves original document code`);
          console.log(`• Restore process preserves original document code`);
          console.log(`• QR code validation works across both tables`);
          console.log(`• No duplicate codes can be generated`);
          
          console.log(`\n🌐 QR Code Validation URLs:`);
          if (uniqueAllCodes.length > 0) {
            uniqueAllCodes.slice(0, 3).forEach((code, index) => {
              console.log(`${index + 1}. http://localhost:3000/validate/${code}`);
            });
          }
          
          resolve();
        });
      });
    });
  });
}

// Execute test
testUniqueCodeGeneration()
  .then(() => {
    console.log('\n✅ Unique code generation test completed');
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
        process.exit(1);
      }
      console.log('🔒 Database connection closed');
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error('❌ Test failed:', error.message);
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
