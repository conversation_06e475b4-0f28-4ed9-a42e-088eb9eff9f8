# LDIS SQLite Database Setup

This document describes the SQLite database setup for the Legal Document Issuance System (LDIS) Next.js project.

## Overview

The database system provides:

- SQLite database with automatic initialization
- User management with secure password hashing
- Auto-creation of tables when the database is first accessed
- Type-safe database operations
- Next.js API route integration

## Database Schema

### Users Table

```sql
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  recovery_key TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Templates Table

```sql
CREATE TABLE templates (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  template_name TEXT NOT NULL,
  description TEXT,
  filename TEXT NOT NULL,
  placeholders TEXT,
  layout_size TEXT,
  uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  user_id INTEGER NOT NULL,
  FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### Documents Table

```sql
CREATE TABLE documents (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  document_name TEXT NOT NULL,
  applicant_name TEXT NOT NULL,
  uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  document_data BLOB,
  status TEXT DEFAULT 'pending',
  approved_at DATETIME,
  user_id INTEGER NOT NULL,
  code TEXT UNIQUE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### Notifications Table

```sql
CREATE TABLE notifications (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  document_id INTEGER NOT NULL,
  document_name TEXT NOT NULL,
  applicant_name TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  user_id INTEGER NOT NULL,
  FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### Archives Table

```sql
CREATE TABLE archives (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  document_name TEXT NOT NULL,
  applicant_name TEXT NOT NULL,
  uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  document_data BLOB,
  user_id INTEGER NOT NULL,
  document_id INTEGER NOT NULL,
  approved_at DATETIME,
  approved_by INTEGER,
  code TEXT,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
);
```

## Files Structure

```
src/lib/database.ts          # Main database utility module
src/app/api/users/route.ts   # Example API routes for user management
scripts/init-db.js           # Database initialization script
scripts/test-db.js           # Database testing script
data/ldis.db                 # SQLite database file (auto-created)
```

## Installation

The required dependencies are already installed:

- `sqlite3` - SQLite database driver
- `bcryptjs` - Password hashing library
- `ts-node` - TypeScript execution for scripts

## Usage

### 1. Database Initialization

The database initializes automatically when first accessed. You can also manually initialize it:

```bash
node scripts/init-db.js
```

### 2. Using Database Functions

```typescript
import {
  initializeDatabase,
  createUser,
  getUserByUsername,
  getAllUsers,
  updateUserPassword,
  createTemplate,
  getTemplatesByUserId,
  getAllTemplates,
  createDocument,
  getDocumentById,
  getDocumentsByUserId,
  getAllDocuments,
  updateDocumentStatus,
  updateDocumentArchiveStatus,
  deleteDocument,
  getDocumentsWithUserInfo,
  createArchive,
  getArchiveById,
  getArchivesByUserId,
  getAllArchives,
  getArchivesByDocumentId,
  updateArchiveApproval,
  deleteArchive,
  getArchivesWithUserInfo,
  getArchivesByApplicantName,
  getArchivesByDocumentName,
} from "@/lib/database";

// Initialize database (optional - happens automatically)
await initializeDatabase();

// Create a new user
const userId = await createUser("john_doe", hashedPassword, "recovery_key_123");

// Get user by username
const user = await getUserByUsername("john_doe");

// Get all users
const users = await getAllUsers();

// Create a new template
const templateId = await createTemplate(
  "Contract Template",
  "Standard contract template for legal documents",
  "contract_template.docx",
  JSON.stringify(["client_name", "date", "amount"]),
  "A4",
  userId
);

// Get templates for a specific user
const userTemplates = await getTemplatesByUserId(userId);

// Get all templates
const allTemplates = await getAllTemplates();

// Create a new document
const documentId = await createDocument(
  "Birth Certificate Application",
  "John Doe",
  documentBuffer, // Buffer containing document data
  "pending",
  userId
);

// Get documents for a specific user
const userDocuments = await getDocumentsByUserId(userId);

// Update document status
await updateDocumentStatus(documentId, "approved", new Date().toISOString());

// Create archive (hard archive - moves document to archives table)
const archiveId = await createArchive(
  document.document_name,
  document.applicant_name,
  document.document_data,
  document.user_id,
  document.id,
  document.code, // Document code for reference
  document.approved_at
);

// Delete original document (completing hard archive)
await deleteDocument(documentId);
```

### 3. API Routes Example

The system includes example API routes at `/api/users`:

- `GET /api/users` - Get all users (passwords excluded)
- `POST /api/users` - Create a new user

Example usage:

```javascript
// Create a new user
const response = await fetch("/api/users", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    username: "john_doe",
    password: "securePassword123",
    recoveryKey: "optional_recovery_key",
  }),
});

// Get all users
const users = await fetch("/api/users").then((res) => res.json());
```

### 4. Password Security

Passwords are automatically hashed using bcryptjs with a salt rounds of 12:

```typescript
import bcrypt from "bcryptjs";

// Hash password before storing
const hashedPassword = await bcrypt.hash(plainPassword, 12);

// Verify password
const isValid = await bcrypt.compare(plainPassword, hashedPassword);
```

## Available Database Functions

### Core Functions

- `initializeDatabase()` - Initialize database connection and create tables
- `getDatabase()` - Get database instance
- `closeDatabase()` - Close database connection
- `runQuery(sql, params)` - Execute a query
- `getRow(sql, params)` - Get single row
- `getAllRows(sql, params)` - Get all rows

### User Management Functions

- `createUser(username, hashedPassword, recoveryKey?)` - Create new user
- `getUserByUsername(username)` - Get user by username
- `getUserById(id)` - Get user by ID
- `updateUserPassword(id, hashedPassword)` - Update user password
- `updateUserRecoveryKey(id, recoveryKey)` - Update recovery key
- `getAllUsers()` - Get all users
- `deleteUser(id)` - Delete user

### Template Management Functions

- `createTemplate(templateName, description, filename, placeholders, layoutSize, userId)` - Create new template
- `getTemplateById(id)` - Get template by ID
- `getTemplatesByUserId(userId)` - Get all templates for a specific user
- `getAllTemplates()` - Get all templates
- `updateTemplate(id, templateName, description, placeholders, layoutSize)` - Update template information
- `deleteTemplate(id)` - Delete template
- `getTemplatesWithUserInfo()` - Get templates with user information (JOIN query)

### Document Management Functions

- `createDocument(documentName, applicantName, documentData, status, userId)` - Create new document
- `getDocumentById(id)` - Get document by ID
- `getDocumentsByUserId(userId)` - Get all documents for a specific user
- `getAllDocuments()` - Get all documents
- `updateDocumentStatus(id, status, approvedAt)` - Update document status and approval date
- `deleteDocument(id)` - Delete document
- `getDocumentsWithUserInfo()` - Get documents with user information (JOIN query)

### Archive Functions

- `createArchive(documentName, applicantName, documentData, userId, documentId, code, approvedAt, approvedBy)` - Create new archive entry
- `getArchiveById(id)` - Get archive by ID
- `getArchivesByUserId(userId)` - Get all archives for a specific user
- `getAllArchives()` - Get all archives
- `getArchivesByDocumentId(documentId)` - Get archives by document ID
- `updateArchiveApproval(id, approvedAt, approvedBy)` - Update archive approval information
- `deleteArchive(id)` - Delete archive
- `getArchivesWithUserInfo()` - Get archives with user information (JOIN query)
- `getArchivesByApplicantName(applicantName)` - Get archives by applicant name
- `getArchivesByDocumentName(documentName)` - Get archives by document name

## Testing

Run the database test script to verify everything works:

```bash
node scripts/test-db.js
```

This will:

1. Initialize the database
2. Create a test user
3. Retrieve and verify the user
4. Test password hashing/verification
5. Display all users

## Database Location

The SQLite database file is created at: `./data/ldis.db` (in the data directory)

## Next.js Integration

The database automatically initializes when imported in a Next.js environment. The initialization only happens on the server-side to avoid issues with client-side rendering.

## Error Handling

All database functions include proper error handling and logging. Errors are logged to the console and thrown as JavaScript Error objects.

## Security Considerations

1. **Password Hashing**: All passwords are hashed using bcryptjs with salt rounds of 12
2. **SQL Injection Prevention**: All queries use parameterized statements
3. **API Security**: The API routes exclude sensitive data (passwords, recovery keys) from responses
4. **Database File**: Ensure the database file is not accessible via web server

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure the application has write permissions to the project directory
2. **Database Locked**: Close any existing database connections before running scripts
3. **Module Not Found**: Ensure all dependencies are installed with `pnpm install`

### Debugging

Enable SQLite verbose mode by modifying the database.ts file:

```typescript
const sqlite = sqlite3.verbose(); // Already enabled
```

This will provide detailed logging of all database operations.
