#!/usr/bin/env node

/**
 * Test script for search and sort functionality
 * This script tests the search and sort utilities with sample data
 */

const { searchFilterSort, createFilterOptions, getUniqueFieldValues } = require("../src/lib/search-sort-utils.ts");

console.log("🔍 Testing Search and Sort Functionality\n");

// Sample notification data
const sampleNotifications = [
  {
    id: 1,
    document_name: "Medical Certificate",
    applicant_name: "<PERSON>",
    uploaded_at: "2025-01-01T10:00:00Z",
    is_read: false,
  },
  {
    id: 2,
    document_name: "Birth Certificate",
    applicant_name: "<PERSON>",
    uploaded_at: "2025-01-02T11:00:00Z",
    is_read: true,
  },
  {
    id: 3,
    document_name: "Marriage Certificate",
    applicant_name: "<PERSON>",
    uploaded_at: "2025-01-03T12:00:00Z",
    is_read: false,
  },
  {
    id: 4,
    document_name: "Death Certificate",
    applicant_name: "<PERSON>",
    uploaded_at: "2025-01-04T13:00:00Z",
    is_read: true,
  },
];

// Sample archive data
const sampleArchives = [
  {
    id: 1,
    document_name: "Old Medical Certificate",
    applicant_name: "<PERSON>",
    uploaded_at: "2024-12-01T10:00:00Z",
    approved_at: "2024-12-02T10:00:00Z",
    approved_by_username: "admin",
  },
  {
    id: 2,
    document_name: "Old Birth Certificate",
    applicant_name: "Jane Smith",
    uploaded_at: "2024-12-03T11:00:00Z",
    approved_at: "2024-12-04T11:00:00Z",
    approved_by_username: "supervisor",
  },
  {
    id: 3,
    document_name: "Old Marriage Certificate",
    applicant_name: "Bob Johnson",
    uploaded_at: "2024-12-05T12:00:00Z",
    approved_at: "2024-12-06T12:00:00Z",
    approved_by_username: "admin",
  },
];

function testNotificationSearch() {
  console.log("📋 Testing Notification Search and Sort:");
  
  // Test search functionality
  console.log("\n🔍 Search Tests:");
  
  const searchResults1 = searchFilterSort(sampleNotifications, {
    searchTerm: "medical",
    searchFields: ["document_name", "applicant_name"],
  });
  console.log(`✅ Search for "medical": Found ${searchResults1.length} results`);
  console.log(`   - ${searchResults1.map(n => n.document_name).join(", ")}`);
  
  const searchResults2 = searchFilterSort(sampleNotifications, {
    searchTerm: "john",
    searchFields: ["document_name", "applicant_name"],
  });
  console.log(`✅ Search for "john": Found ${searchResults2.length} results`);
  console.log(`   - ${searchResults2.map(n => n.applicant_name).join(", ")}`);
  
  // Test filter functionality
  console.log("\n🔽 Filter Tests:");
  
  const filterResults1 = searchFilterSort(sampleNotifications, {
    filters: [{ field: "is_read", values: ["false"] }],
  });
  console.log(`✅ Filter unread: Found ${filterResults1.length} results`);
  console.log(`   - ${filterResults1.map(n => n.document_name).join(", ")}`);
  
  const filterResults2 = searchFilterSort(sampleNotifications, {
    filters: [{ field: "is_read", values: ["true"] }],
  });
  console.log(`✅ Filter read: Found ${filterResults2.length} results`);
  console.log(`   - ${filterResults2.map(n => n.document_name).join(", ")}`);
  
  // Test sort functionality
  console.log("\n📊 Sort Tests:");
  
  const sortResults1 = searchFilterSort(sampleNotifications, {
    sortField: "document_name",
    sortOrder: "asc",
  });
  console.log(`✅ Sort by document name (asc):`);
  console.log(`   - ${sortResults1.map(n => n.document_name).join(", ")}`);
  
  const sortResults2 = searchFilterSort(sampleNotifications, {
    sortField: "uploaded_at",
    sortOrder: "desc",
  });
  console.log(`✅ Sort by upload date (desc):`);
  console.log(`   - ${sortResults2.map(n => n.document_name).join(", ")}`);
  
  // Test combined search, filter, and sort
  console.log("\n🔄 Combined Tests:");
  
  const combinedResults = searchFilterSort(sampleNotifications, {
    searchTerm: "certificate",
    searchFields: ["document_name"],
    filters: [{ field: "is_read", values: ["false"] }],
    sortField: "applicant_name",
    sortOrder: "asc",
  });
  console.log(`✅ Search "certificate" + filter unread + sort by applicant:`);
  console.log(`   - Found ${combinedResults.length} results`);
  combinedResults.forEach(n => {
    console.log(`     * ${n.document_name} - ${n.applicant_name} (unread: ${!n.is_read})`);
  });
}

function testArchiveSearch() {
  console.log("\n\n📁 Testing Archive Search and Sort:");
  
  // Test search functionality
  console.log("\n🔍 Search Tests:");
  
  const searchResults1 = searchFilterSort(sampleArchives, {
    searchTerm: "medical",
    searchFields: ["document_name", "applicant_name", "approved_by_username"],
  });
  console.log(`✅ Search for "medical": Found ${searchResults1.length} results`);
  console.log(`   - ${searchResults1.map(a => a.document_name).join(", ")}`);
  
  const searchResults2 = searchFilterSort(sampleArchives, {
    searchTerm: "admin",
    searchFields: ["document_name", "applicant_name", "approved_by_username"],
  });
  console.log(`✅ Search for "admin": Found ${searchResults2.length} results`);
  console.log(`   - ${searchResults2.map(a => `${a.document_name} (by ${a.approved_by_username})`).join(", ")}`);
  
  // Test filter functionality
  console.log("\n🔽 Filter Tests:");
  
  const filterResults1 = searchFilterSort(sampleArchives, {
    filters: [{ field: "approved_by_username", values: ["admin"] }],
  });
  console.log(`✅ Filter by admin: Found ${filterResults1.length} results`);
  console.log(`   - ${filterResults1.map(a => a.document_name).join(", ")}`);
  
  // Test sort functionality
  console.log("\n📊 Sort Tests:");
  
  const sortResults1 = searchFilterSort(sampleArchives, {
    sortField: "approved_at",
    sortOrder: "desc",
  });
  console.log(`✅ Sort by approved date (desc):`);
  console.log(`   - ${sortResults1.map(a => `${a.document_name} (${a.approved_at})`).join(", ")}`);
  
  // Test filter options generation
  console.log("\n⚙️  Filter Options Generation:");
  
  const uniqueUsers = getUniqueFieldValues(sampleArchives, "approved_by_username");
  console.log(`✅ Unique approved_by users: ${uniqueUsers.join(", ")}`);
  
  const filterOptions = createFilterOptions(sampleArchives, "approved_by_username", (value) => `User: ${value}`);
  console.log(`✅ Generated filter options:`);
  filterOptions.forEach(option => {
    console.log(`   - ${option.label} (value: ${option.value})`);
  });
}

function testEdgeCases() {
  console.log("\n\n🧪 Testing Edge Cases:");
  
  // Empty search
  const emptySearch = searchFilterSort(sampleNotifications, {
    searchTerm: "",
    searchFields: ["document_name"],
  });
  console.log(`✅ Empty search: Returns ${emptySearch.length} results (should be all)`);
  
  // No matches
  const noMatches = searchFilterSort(sampleNotifications, {
    searchTerm: "nonexistent",
    searchFields: ["document_name"],
  });
  console.log(`✅ No matches search: Returns ${noMatches.length} results (should be 0)`);
  
  // Empty filters
  const emptyFilters = searchFilterSort(sampleNotifications, {
    filters: [],
  });
  console.log(`✅ Empty filters: Returns ${emptyFilters.length} results (should be all)`);
  
  // Invalid sort field (should not crash)
  try {
    const invalidSort = searchFilterSort(sampleNotifications, {
      sortField: "nonexistent_field",
      sortOrder: "asc",
    });
    console.log(`✅ Invalid sort field: Returns ${invalidSort.length} results (should handle gracefully)`);
  } catch (error) {
    console.log(`❌ Invalid sort field caused error: ${error.message}`);
  }
}

// Run all tests
try {
  testNotificationSearch();
  testArchiveSearch();
  testEdgeCases();
  
  console.log("\n\n🎉 All search and sort tests completed successfully!");
  console.log("\n📱 Features Tested:");
  console.log("✅ Text search across multiple fields");
  console.log("✅ Filter by specific field values");
  console.log("✅ Sort by different fields (asc/desc)");
  console.log("✅ Combined search + filter + sort");
  console.log("✅ Dynamic filter option generation");
  console.log("✅ Edge case handling");
  
  console.log("\n🎯 Ready for Integration:");
  console.log("• Notifications page: Search by document/applicant, filter by read status");
  console.log("• Archives page: Search by document/applicant/user, filter by archived user");
  console.log("• Both pages: Sort by any column, combine all operations");
  
} catch (error) {
  console.error("❌ Test failed:", error.message);
  process.exit(1);
}
