const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'ldis.db');

console.log('🕒 Testing Notification Timestamp Fix...');
console.log('📍 Database path:', dbPath);

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Test notification timestamp functionality
async function testNotificationTimestamp() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log('\n🔍 Checking current notifications...');
      
      // Get current notifications with timestamps
      db.all(`SELECT id, document_name, applicant_name, uploaded_at, is_read FROM notifications ORDER BY id DESC LIMIT 10`, (err, notifications) => {
        if (err) {
          console.error('❌ Error fetching notifications:', err.message);
          reject(err);
          return;
        }
        
        console.log(`📋 Found ${notifications.length} recent notifications:`);
        
        if (notifications.length > 0) {
          notifications.forEach((notification, index) => {
            const uploadedAt = new Date(notification.uploaded_at);
            const now = new Date();
            const timeDiff = Math.abs(now - uploadedAt);
            const minutesAgo = Math.floor(timeDiff / (1000 * 60));
            const hoursAgo = Math.floor(timeDiff / (1000 * 60 * 60));
            
            let timeDisplay;
            if (minutesAgo < 60) {
              timeDisplay = `${minutesAgo} minutes ago`;
            } else {
              timeDisplay = `${hoursAgo} hours ago`;
            }
            
            console.log(`   ${index + 1}. ${notification.document_name}`);
            console.log(`      Applicant: ${notification.applicant_name}`);
            console.log(`      Uploaded: ${notification.uploaded_at}`);
            console.log(`      Time ago: ${timeDisplay}`);
            console.log(`      Read: ${notification.is_read ? 'Yes' : 'No'}`);
            console.log('');
          });
        } else {
          console.log('   No notifications found.');
        }
        
        // Test creating a new notification with current timestamp
        console.log('🧪 Testing notification creation with current timestamp...');
        
        // First, get a user and document to create notification for
        db.get(`SELECT id FROM users LIMIT 1`, (err, user) => {
          if (err) {
            console.error('❌ Error fetching user:', err.message);
            reject(err);
            return;
          }
          
          if (!user) {
            console.log('⚠️  No users found. Creating test notification requires a user.');
            resolve();
            return;
          }
          
          db.get(`SELECT id, document_name, applicant_name FROM documents LIMIT 1`, (err, document) => {
            if (err) {
              console.error('❌ Error fetching document:', err.message);
              reject(err);
              return;
            }
            
            if (!document) {
              console.log('⚠️  No documents found. Creating test notification requires a document.');
              resolve();
              return;
            }
            
            // Create a test notification with current timestamp
            const currentTimestamp = new Date().toISOString();
            console.log(`📝 Creating test notification at: ${currentTimestamp}`);
            
            db.run(
              `INSERT INTO notifications (document_id, document_name, applicant_name, user_id, uploaded_at) VALUES (?, ?, ?, ?, ?)`,
              [document.id, `TEST: ${document.document_name}`, document.applicant_name, user.id, currentTimestamp],
              function(err) {
                if (err) {
                  console.error('❌ Error creating test notification:', err.message);
                  reject(err);
                  return;
                }
                
                const testNotificationId = this.lastID;
                console.log(`✅ Test notification created with ID: ${testNotificationId}`);
                
                // Verify the notification was created with correct timestamp
                db.get(`SELECT * FROM notifications WHERE id = ?`, [testNotificationId], (err, newNotification) => {
                  if (err) {
                    console.error('❌ Error fetching test notification:', err.message);
                    reject(err);
                    return;
                  }
                  
                  console.log('\n🔍 Verifying test notification timestamp...');
                  console.log(`📄 Document: ${newNotification.document_name}`);
                  console.log(`👤 Applicant: ${newNotification.applicant_name}`);
                  console.log(`🕒 Created at: ${newNotification.uploaded_at}`);
                  console.log(`📅 Expected: ${currentTimestamp}`);
                  
                  const createdTime = new Date(newNotification.uploaded_at);
                  const expectedTime = new Date(currentTimestamp);
                  const timeDifference = Math.abs(createdTime - expectedTime);
                  
                  if (timeDifference < 1000) { // Less than 1 second difference
                    console.log('✅ Timestamp is correct! (within 1 second)');
                  } else {
                    console.log(`⚠️  Timestamp difference: ${timeDifference}ms`);
                  }
                  
                  // Clean up test notification
                  db.run(`DELETE FROM notifications WHERE id = ?`, [testNotificationId], (err) => {
                    if (err) {
                      console.error('❌ Error cleaning up test notification:', err.message);
                    } else {
                      console.log('🧹 Test notification cleaned up');
                    }
                    
                    console.log('\n🔧 Fix Applied:');
                    console.log('✅ createNotification() now explicitly sets uploaded_at timestamp');
                    console.log('✅ Uses new Date().toISOString() for current time');
                    console.log('✅ No longer relies on database DEFAULT CURRENT_TIMESTAMP');
                    
                    console.log('\n📱 Expected Behavior:');
                    console.log('• New notifications should show "now" or "1 minute ago"');
                    console.log('• Timestamps should reflect actual creation time');
                    console.log('• No more "8h ago" for new notifications');
                    
                    console.log('\n🎯 To Test:');
                    console.log('1. Upload a new document in LDIS');
                    console.log('2. Check notifications panel');
                    console.log('3. New notification should show current time');
                    console.log('4. Refresh page - timestamp should remain current');
                    
                    resolve();
                  });
                });
              }
            );
          });
        });
      });
    });
  });
}

// Execute test
testNotificationTimestamp()
  .then(() => {
    console.log('\n✅ Notification timestamp test completed');
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
        process.exit(1);
      }
      console.log('🔒 Database connection closed');
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error('❌ Test failed:', error.message);
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
