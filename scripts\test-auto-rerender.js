const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'ldis.db');

console.log('🔄 Testing automatic preview re-rendering after approval...');
console.log('📍 Database path:', dbPath);

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Test the auto re-render functionality
async function testAutoRerender() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log('\n🔍 Checking documents for auto re-render testing...');
      
      // Get all documents (approved and non-approved)
      db.all(`SELECT id, document_name, status, approved_at FROM documents ORDER BY id`, (err, docs) => {
        if (err) {
          console.error('❌ Error fetching documents:', err.message);
          reject(err);
          return;
        }
        
        console.log(`📄 Found ${docs.length} documents:`);
        
        const approvedDocs = docs.filter(doc => doc.status === 'approved');
        const pendingDocs = docs.filter(doc => doc.status === 'pending');
        const otherDocs = docs.filter(doc => doc.status !== 'approved' && doc.status !== 'pending');
        
        console.log('\n📊 Document Status Summary:');
        console.log(`✅ Approved: ${approvedDocs.length} documents`);
        console.log(`⏳ Pending: ${pendingDocs.length} documents`);
        console.log(`📋 Other: ${otherDocs.length} documents`);
        
        if (approvedDocs.length > 0) {
          console.log('\n✅ Approved Documents (already have QR codes):');
          approvedDocs.forEach((doc, index) => {
            console.log(`${index + 1}. ${doc.document_name} (ID: ${doc.id})`);
          });
        }
        
        if (pendingDocs.length > 0) {
          console.log('\n⏳ Pending Documents (perfect for testing auto re-render):');
          pendingDocs.forEach((doc, index) => {
            console.log(`${index + 1}. ${doc.document_name} (ID: ${doc.id})`);
          });
          
          console.log('\n🎯 Auto Re-render Test Instructions:');
          console.log('1. Open the LDIS application in your browser');
          console.log('2. Navigate to Administration > Documents');
          console.log('3. Click on any PENDING document from the list above');
          console.log('4. Notice that NO QR code is visible in the preview (document is not approved)');
          console.log('5. Click the "Edit" button');
          console.log('6. Click the "Approved" button to approve the document');
          console.log('7. 🔄 The preview should AUTOMATICALLY re-render and show the QR code');
          console.log('8. You should NOT need to refresh the page to see the QR code');
          console.log('9. If the QR code appears immediately after approval, the auto re-render is working! ✅');
          
        } else {
          console.log('\n⚠️  No pending documents found for testing.');
          console.log('💡 To test auto re-render:');
          console.log('   1. Create a new document by going to Apply > [Template]');
          console.log('   2. Fill out the form and submit');
          console.log('   3. Go to Administration > Documents');
          console.log('   4. Click on the new document (it will be pending)');
          console.log('   5. Follow the test instructions above');
        }
        
        console.log('\n🔧 Technical Details:');
        console.log('- When a document is approved, handleApprove() now calls:');
        console.log('  • generatePreview() for template-based documents');
        console.log('  • generateSimplePreview() for simple documents');
        console.log('- Both functions check the updated document status and add QR codes');
        console.log('- The preview re-renders automatically without page refresh');
        console.log('- QR codes appear in the bottom-left corner (60x60px)');
        
        resolve();
      });
    });
  });
}

// Execute test
testAutoRerender()
  .then(() => {
    console.log('\n✅ Auto re-render test setup completed');
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
        process.exit(1);
      }
      console.log('🔒 Database connection closed');
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error('❌ Test failed:', error.message);
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
