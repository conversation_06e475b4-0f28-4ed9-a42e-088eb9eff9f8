import { NextRequest, NextResponse } from "next/server";
import { getDocumentByCode, getArchiveByCode } from "@/lib/database";

export async function GET(
  request: NextRequest,
  { params }: { params: { code: string } }
) {
  try {
    const { code } = params;

    if (!code) {
      return NextResponse.json(
        { error: "Code is required" },
        { status: 400 }
      );
    }

    // First check in documents table
    const document = await getDocumentByCode(code);
    if (document) {
      return NextResponse.json({
        valid: document.status === "approved",
        type: "document",
        data: {
          id: document.id,
          document_name: document.document_name,
          applicant_name: document.applicant_name,
          status: document.status,
          approved_at: document.approved_at,
          code: document.code,
        },
      });
    }

    // If not found in documents, check in archives table
    const archive = await getArchiveByCode(code);
    if (archive) {
      return NextResponse.json({
        valid: archive.status === "approved",
        type: "archive",
        data: {
          id: archive.id,
          document_name: archive.document_name,
          applicant_name: archive.applicant_name,
          status: archive.status,
          approved_at: archive.approved_at,
          code: archive.code,
        },
      });
    }

    // Code not found in either table
    return NextResponse.json({
      valid: false,
      error: "Document not found",
    });

  } catch (error) {
    console.error("Validation error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
