#!/usr/bin/env node

/**
 * Test script to verify the archive API endpoint functionality
 */

require("ts-node/register");

async function testArchiveAPIEndpoint() {
  try {
    console.log("🧪 Testing Archive API Endpoint...\n");

    const {
      initializeDatabase,
      createDocument,
      getDocumentById,
      getAllArchives,
      getArchivesWithUserInfo,
      getAllUsers,
      closeDatabase,
    } = require("../src/lib/database.ts");

    // Initialize database
    console.log("📦 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized\n");

    // Get users
    const users = await getAllUsers();
    if (users.length === 0) {
      console.log("❌ No users found. Please create a user first.");
      return;
    }
    const testUser = users[0];
    console.log(`✅ Using test user: ${testUser.username} (ID: ${testUser.id})\n`);

    // Create a test document
    console.log("📄 Creating test document...");
    const documentId = await createDocument(
      "API Test Document",
      "API Test Applicant",
      Buffer.from("Test document data for API test"),
      "approved",
      testUser.id
    );
    console.log(`✅ Test document created with ID: ${documentId}\n`);

    // Check archives before API call
    console.log("📋 Checking archives before API call...");
    const archivesBefore = await getAllArchives();
    console.log(`✅ Archives before: ${archivesBefore.length}\n`);

    // Simulate the API call by importing and calling the archive route logic
    console.log("🔗 Simulating archive API call...");
    
    // Get the document
    const document = await getDocumentById(documentId);
    if (!document) {
      console.log("❌ Document not found");
      return;
    }

    if (document.status !== 'approved') {
      console.log("❌ Document is not approved");
      return;
    }

    // Get current user (simulating the API logic)
    const currentUser = users.length > 0 ? users[0] : null;
    
    console.log('Creating archive for document:', {
      id: document.id,
      name: document.document_name,
      applicant: document.applicant_name,
      status: document.status,
      archivedBy: currentUser?.username
    });

    // Create archive entry with current user as the approver/archiver
    const { createArchive, deleteDocument } = require("../src/lib/database.ts");
    
    const archiveId = await createArchive(
      document.document_name,
      document.applicant_name,
      document.document_data,
      document.user_id,
      document.id,
      document.approved_at || undefined,
      currentUser?.id // Set the current user as the one who approved/archived
    );

    console.log(`✅ Archive created with ID: ${archiveId}\n`);

    // Delete the original document (hard archive)
    await deleteDocument(documentId);
    console.log("✅ Original document deleted\n");

    // Check archives after API call
    console.log("📋 Checking archives after API call...");
    const archivesAfter = await getArchivesWithUserInfo();
    console.log(`✅ Archives after: ${archivesAfter.length}\n`);

    // Find our test archive
    const testArchive = archivesAfter.find(a => a.id === archiveId);
    if (testArchive) {
      console.log("📋 Archive Details (from API simulation):");
      console.log(`   Document Name: ${testArchive.document_name}`);
      console.log(`   Applicant Name: ${testArchive.applicant_name}`);
      console.log(`   Uploaded By: ${testArchive.username || 'Unknown'}`);
      console.log(`   Archived By: ${testArchive.approved_by_username || 'Unknown'}`);
      console.log(`   Approved At: ${testArchive.approved_at || 'Not set'}`);
      console.log(`   Archive ID: ${testArchive.id}\n`);

      // Verify the functionality
      if (testArchive.approved_by_username === testUser.username) {
        console.log("🎉 SUCCESS: Archive API endpoint simulation completed successfully!");
        console.log(`   ✅ Archive created with correct 'archived by' username: ${testUser.username}`);
        console.log(`   ✅ Original document deleted (hard archive)`);
        console.log(`   ✅ Archive persisted with user information`);
      } else {
        console.log(`❌ FAILURE: Expected archived_by_username to be '${testUser.username}', got '${testArchive.approved_by_username}'`);
      }
    } else {
      console.log("❌ FAILURE: Could not find the test archive after API simulation");
    }

    console.log("\n📊 API Simulation Summary:");
    console.log(`   Archives before: ${archivesBefore.length}`);
    console.log(`   Archives after: ${archivesAfter.length}`);
    console.log(`   Archive ID: ${archiveId}`);
    console.log(`   Archived By: ${testArchive?.approved_by_username || 'Not found'}`);
    console.log(`   Test Result: ${testArchive?.approved_by_username === testUser.username ? '✅ PASSED' : '❌ FAILED'}`);

    await closeDatabase();
  } catch (error) {
    console.error("❌ Error in archive API endpoint test:", error);
    process.exit(1);
  }
}

testArchiveAPIEndpoint();
