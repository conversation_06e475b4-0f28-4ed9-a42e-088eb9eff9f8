const { getDatabase } = require('../src/lib/database.ts');

async function debugDateTime() {
  console.log('🔍 Debugging DateTime Comparison...\n');

  try {
    const db = await getDatabase();
    
    // Get current time in different formats
    const jsNow = new Date().toISOString();
    const pastTime = new Date(Date.now() - 60000).toISOString(); // 1 minute ago
    
    console.log('JavaScript ISO String (now):', jsNow);
    console.log('JavaScript ISO String (past):', pastTime);
    
    // Check SQLite's datetime("now")
    db.get('SELECT datetime("now") as sqlite_now', (err, row) => {
      if (err) {
        console.error('Error:', err);
        return;
      }
      console.log('SQLite datetime("now"):', row.sqlite_now);
      
      // Test comparison
      db.get('SELECT ? > datetime("now") as is_future, ? < datetime("now") as is_past', [jsNow, pastTime], (err, row) => {
        if (err) {
          console.error('Error:', err);
          return;
        }
        console.log('JS now > SQLite now:', row.is_future);
        console.log('JS past < SQLite now:', row.is_past);
        
        // Check actual verification codes
        db.all('SELECT id, email, code, expires_at, datetime("now") as current_time, expires_at > datetime("now") as is_valid FROM verification_codes', (err, rows) => {
          if (err) {
            console.error('Error:', err);
            return;
          }
          
          console.log('\nVerification codes in database:');
          rows.forEach(row => {
            console.log(`ID: ${row.id}, Email: ${row.email}, Code: ${row.code}`);
            console.log(`  Expires: ${row.expires_at}`);
            console.log(`  Current: ${row.current_time}`);
            console.log(`  Valid: ${row.is_valid}`);
            console.log('');
          });
          
          process.exit(0);
        });
      });
    });

  } catch (error) {
    console.error('❌ Debug failed:', error);
    process.exit(1);
  }
}

// Run the debug
debugDateTime();
