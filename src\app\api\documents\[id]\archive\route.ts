import { NextRequest, NextResponse } from 'next/server';
import {
  getDocumentById,
  createArchive,
  deleteDocument,
  getAllUsers
} from '@/lib/database';

/**
 * POST /api/documents/[id]/archive - Archive a document (hard archive)
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const documentId = parseInt(id);
    
    if (isNaN(documentId)) {
      return NextResponse.json(
        { error: 'Invalid document ID' },
        { status: 400 }
      );
    }

    // Get document from database first to verify it exists
    const document = await getDocumentById(documentId);
    
    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Check if document is approved
    if (document.status !== 'approved') {
      return NextResponse.json(
        { error: 'Only approved documents can be archived' },
        { status: 400 }
      );
    }

    // Get current user (the one performing the archive action)
    const users = await getAllUsers();
    const currentUser = users.length > 0 ? users[0] : null; // Single user system

    console.log('Creating archive for document:', {
      id: document.id,
      name: document.document_name,
      applicant: document.applicant_name,
      status: document.status,
      archivedBy: currentUser?.username
    });

    // Create archive entry with current user as the approver/archiver
    const archiveId = await createArchive(
      document.document_name,
      document.applicant_name,
      document.document_data,
      document.user_id,
      document.id,
      document.code || '', // Pass the document code to the archive
      document.approved_at || undefined,
      currentUser?.id, // Set the current user as the one who approved/archived
      document.status // Pass the document status to the archive
    );

    console.log('Archive created with ID:', archiveId);

    // Delete the original document (hard archive)
    await deleteDocument(documentId);

    console.log('Original document deleted, ID:', documentId);

    return NextResponse.json(
      { 
        message: 'Document archived successfully',
        archiveId: archiveId
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error archiving document:', error);
    return NextResponse.json(
      { error: 'Failed to archive document' },
      { status: 500 }
    );
  }
}
