/**
 * Validation utility functions for LDIS authentication system
 */

export interface ValidationResult {
  isValid: boolean;
  message?: string;
}



/**
 * Validate password strength and format
 */
export function validatePassword(password: string): ValidationResult {
  if (!password) {
    return {
      isValid: false,
      message: "Password is required",
    };
  }

  if (password.length < 6) {
    return {
      isValid: false,
      message: "Password must be at least 6 characters long",
    };
  }

  if (password.length > 128) {
    return {
      isValid: false,
      message: "Password must be less than 128 characters long",
    };
  }

  return { isValid: true };
}

/**
 * Validate username format and requirements
 */
export function validateUsername(username: string): ValidationResult {
  if (!username) {
    return {
      isValid: false,
      message: "Username is required",
    };
  }

  if (username.length < 3) {
    return {
      isValid: false,
      message: "Username must be at least 3 characters long",
    };
  }

  if (username.length > 50) {
    return {
      isValid: false,
      message: "Username must be less than 50 characters long",
    };
  }

  if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
    return {
      isValid: false,
      message: "Username can only contain letters, numbers, hyphens, and underscores",
    };
  }

  return { isValid: true };
}

/**
 * Validate verification code format
 */
export function validateVerificationCode(code: string): ValidationResult {
  if (!code) {
    return {
      isValid: false,
      message: "Verification code is required",
    };
  }

  if (!/^\d{6}$/.test(code)) {
    return {
      isValid: false,
      message: "Verification code must be exactly 6 digits",
    };
  }

  return { isValid: true };
}

/**
 * Validate recovery key format
 */
export function validateRecoveryKey(recoveryKey: string): ValidationResult {
  if (!recoveryKey) {
    return {
      isValid: false,
      message: "Recovery key is required",
    };
  }

  if (recoveryKey.length < 8) {
    return {
      isValid: false,
      message: "Recovery key must be at least 8 characters long",
    };
  }

  return { isValid: true };
}

/**
 * Validate form data for user signup
 */
export function validateUserSignupData(data: {
  username: string;
  password: string;
}): ValidationResult {
  const usernameValidation = validateUsername(data.username);
  if (!usernameValidation.isValid) {
    return usernameValidation;
  }

  const passwordValidation = validatePassword(data.password);
  if (!passwordValidation.isValid) {
    return passwordValidation;
  }

  return { isValid: true };
}

/**
 * Validate form data for admin signup
 */
export function validateAdminSignupData(data: {
  username: string;
  password: string;
  recoveryKey?: string;
}): ValidationResult {
  const usernameValidation = validateUsername(data.username);
  if (!usernameValidation.isValid) {
    return usernameValidation;
  }

  const passwordValidation = validatePassword(data.password);
  if (!passwordValidation.isValid) {
    return passwordValidation;
  }

  // Recovery key is optional for admin signup (auto-generated if not provided)
  if (data.recoveryKey) {
    const recoveryKeyValidation = validateRecoveryKey(data.recoveryKey);
    if (!recoveryKeyValidation.isValid) {
      return recoveryKeyValidation;
    }
  }

  return { isValid: true };
}

/**
 * Validate signin data
 */
export function validateSigninData(data: {
  username: string;
  password: string;
}): ValidationResult {
  const usernameValidation = validateUsername(data.username);
  if (!usernameValidation.isValid) {
    return usernameValidation;
  }

  const passwordValidation = validatePassword(data.password);
  if (!passwordValidation.isValid) {
    return passwordValidation;
  }

  return { isValid: true };
}
