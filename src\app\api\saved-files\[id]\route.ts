import { NextRequest, NextResponse } from 'next/server';
import { getSavedFileById, deleteSavedFile } from '@/lib/database';

/**
 * GET /api/saved-files/[id] - Get a specific saved file
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid file ID' },
        { status: 400 }
      );
    }

    const savedFile = await getSavedFileById(id);
    
    if (!savedFile) {
      return NextResponse.json(
        { error: 'Saved file not found' },
        { status: 404 }
      );
    }

    // Return file as blob
    return new NextResponse(savedFile.document_data, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="saved-file-${id}.pdf"`,
      },
    });
    
  } catch (error) {
    console.error('Error fetching saved file:', error);
    return NextResponse.json(
      { error: 'Failed to fetch saved file' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/saved-files/[id] - Delete a specific saved file
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid file ID' },
        { status: 400 }
      );
    }

    // Check if file exists
    const savedFile = await getSavedFileById(id);
    if (!savedFile) {
      return NextResponse.json(
        { error: 'Saved file not found' },
        { status: 404 }
      );
    }

    // Delete the file
    await deleteSavedFile(id);
    
    return NextResponse.json(
      { message: 'Saved file deleted successfully' },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error deleting saved file:', error);
    return NextResponse.json(
      { error: 'Failed to delete saved file' },
      { status: 500 }
    );
  }
}
